<?php
/**
 * The template for displaying comments
 *
 * @package VelocityShop
 * @version 1.0.0
 */

if (post_password_required()) {
    return;
}
?>

<div id="comments" class="comments-area">

    <?php if (have_comments()) : ?>
        <h2 class="comments-title">
            <?php
            $comment_count = get_comments_number();
            if ('1' === $comment_count) {
                printf(
                    esc_html__('One thought on &ldquo;%1$s&rdquo;', 'velocityshop'),
                    '<span>' . get_the_title() . '</span>'
                );
            } else {
                printf(
                    esc_html(_nx(
                        '%1$s thought on &ldquo;%2$s&rdquo;',
                        '%1$s thoughts on &ldquo;%2$s&rdquo;',
                        $comment_count,
                        'comments title',
                        'velocityshop'
                    )),
                    number_format_i18n($comment_count),
                    '<span>' . get_the_title() . '</span>'
                );
            }
            ?>
        </h2>

        <?php the_comments_navigation(); ?>

        <ol class="comment-list">
            <?php
            wp_list_comments(array(
                'style'      => 'ol',
                'short_ping' => true,
                'callback'   => 'velocityshop_comment_callback',
            ));
            ?>
        </ol>

        <?php
        the_comments_navigation();

        if (!comments_open()) :
        ?>
            <p class="no-comments"><?php esc_html_e('Comments are closed.', 'velocityshop'); ?></p>
        <?php
        endif;

    endif; // Check for have_comments().

    comment_form();
    ?>

</div>

<?php
/**
 * Custom comment callback
 */
function velocityshop_comment_callback($comment, $args, $depth) {
    if ('div' === $args['style']) {
        $tag       = 'div';
        $add_below = 'comment';
    } else {
        $tag       = 'li';
        $add_below = 'div-comment';
    }
    ?>
    <<?php echo $tag; ?> <?php comment_class(empty($args['has_children']) ? '' : 'parent'); ?> id="comment-<?php comment_ID(); ?>">
    <?php if ('div' != $args['style']) : ?>
        <div id="div-comment-<?php comment_ID(); ?>" class="comment-body">
    <?php endif; ?>
    
    <div class="comment-author vcard">
        <?php if ($args['avatar_size'] != 0) echo get_avatar($comment, $args['avatar_size']); ?>
        <div class="comment-metadata">
            <?php printf(__('<cite class="fn">%s</cite> <span class="says">says:</span>', 'velocityshop'), get_comment_author_link()); ?>
            <a href="<?php echo htmlspecialchars(get_comment_link($comment->comment_ID)); ?>">
                <?php
                printf(
                    __('%1$s at %2$s', 'velocityshop'),
                    get_comment_date(),
                    get_comment_time()
                );
                ?>
            </a>
            <?php edit_comment_link(__('(Edit)', 'velocityshop'), '  ', ''); ?>
        </div>
    </div>

    <?php if ($comment->comment_approved == '0') : ?>
        <em class="comment-awaiting-moderation"><?php _e('Your comment is awaiting moderation.', 'velocityshop'); ?></em>
        <br />
    <?php endif; ?>

    <div class="comment-content">
        <?php comment_text(); ?>
    </div>

    <div class="reply">
        <?php comment_reply_link(array_merge($args, array('add_below' => $add_below, 'depth' => $depth, 'max_depth' => $args['max_depth']))); ?>
    </div>
    
    <?php if ('div' != $args['style']) : ?>
        </div>
    <?php endif; ?>
    <?php
}
?>

<style>
.comments-area {
    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 2px solid #eee;
}

.comments-title {
    margin-bottom: 2rem;
    color: #2c3e50;
    font-size: 1.5rem;
}

.comment-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.comment-list .comment {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.comment-list .comment.parent {
    background: #fff;
    border: 1px solid #eee;
}

.comment-list .children {
    list-style: none;
    margin-top: 1rem;
    margin-left: 2rem;
}

.comment-list .children .comment {
    background: #fff;
    border-left-color: #95a5a6;
}

.comment-author {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.comment-author .avatar {
    border-radius: 50%;
    flex-shrink: 0;
}

.comment-metadata {
    flex: 1;
}

.comment-metadata .fn {
    font-weight: 600;
    color: #2c3e50;
    font-style: normal;
}

.comment-metadata .says {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.comment-metadata a {
    color: #7f8c8d;
    text-decoration: none;
    font-size: 0.9rem;
    display: block;
    margin-top: 0.25rem;
}

.comment-metadata a:hover {
    color: #3498db;
}

.comment-awaiting-moderation {
    color: #e67e22;
    font-style: italic;
    background: #fef9e7;
    padding: 0.5rem;
    border-radius: 4px;
    display: block;
    margin: 1rem 0;
}

.comment-content {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.comment-content p {
    margin-bottom: 1rem;
}

.comment-content p:last-child {
    margin-bottom: 0;
}

.reply {
    text-align: right;
}

.reply a {
    color: #3498db;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.reply a:hover {
    color: #2980b9;
}

/* Comment Form */
.comment-respond {
    margin-top: 3rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.comment-reply-title {
    margin-bottom: 1.5rem;
    color: #2c3e50;
    font-size: 1.3rem;
}

.comment-form-comment label,
.comment-form-author label,
.comment-form-email label,
.comment-form-url label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.comment-form-comment textarea,
.comment-form-author input,
.comment-form-email input,
.comment-form-url input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.comment-form-comment textarea:focus,
.comment-form-author input:focus,
.comment-form-email input:focus,
.comment-form-url input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.comment-form-comment {
    margin-bottom: 1.5rem;
}

.comment-form-author,
.comment-form-email,
.comment-form-url {
    margin-bottom: 1rem;
}

.form-submit {
    margin-top: 1rem;
}

.form-submit #submit {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-submit #submit:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

/* Comment Navigation */
.comment-navigation {
    margin: 2rem 0;
    text-align: center;
}

.comment-navigation .nav-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comment-navigation a {
    color: #3498db;
    text-decoration: none;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border: 1px solid #3498db;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.comment-navigation a:hover {
    background: #3498db;
    color: white;
}

.no-comments {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .comment-list .children {
        margin-left: 1rem;
    }
    
    .comment-author {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .comment-respond {
        padding: 1.5rem;
    }
    
    .comment-navigation .nav-links {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>
