<?php
/**
 * The template for displaying the footer
 *
 * @package VelocityShop
 * @version 1.0.0
 */
?>

    <footer id="colophon" class="site-footer">
        <?php if (is_active_sidebar('footer-widgets')) : ?>
            <div class="footer-widgets">
                <div class="container">
                    <div class="row">
                        <?php dynamic_sidebar('footer-widgets'); ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="footer-bottom">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="site-info">
                            <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. <?php _e('All rights reserved.', 'velocityshop'); ?></p>
                            <?php
                            printf(
                                esc_html__('Powered by %1$s and %2$s', 'velocityshop'),
                                '<a href="' . esc_url(__('https://wordpress.org/', 'velocityshop')) . '">WordPress</a>',
                                '<a href="#" rel="designer">VelocityShop</a>'
                            );
                            ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <?php if (has_nav_menu('footer')) : ?>
                            <nav class="footer-navigation" role="navigation" aria-label="<?php _e('Footer Menu', 'velocityshop'); ?>">
                                <?php
                                wp_nav_menu(array(
                                    'theme_location' => 'footer',
                                    'menu_id'        => 'footer-menu',
                                    'menu_class'     => 'footer-menu',
                                    'container'      => false,
                                    'depth'          => 1,
                                ));
                                ?>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="back-to-top" aria-label="<?php _e('Back to top', 'velocityshop'); ?>" style="display: none;">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
        </svg>
    </button>

</div><!-- #page -->

<?php wp_footer(); ?>

<style>
/* Footer Styles */
.site-footer {
    background: #2c3e50;
    color: #ecf0f1;
    margin-top: 4rem;
}

.footer-widgets {
    padding: 3rem 0;
    border-bottom: 1px solid #34495e;
}

.footer-widgets .widget {
    margin-bottom: 2rem;
}

.footer-widgets .widget-title {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-widgets .widget ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-widgets .widget ul li {
    margin-bottom: 0.5rem;
}

.footer-widgets .widget ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-widgets .widget ul li a:hover {
    color: #3498db;
}

.footer-bottom {
    padding: 2rem 0;
    background: #34495e;
}

.site-info {
    font-size: 0.9rem;
    color: #bdc3c7;
}

.site-info a {
    color: #3498db;
    text-decoration: none;
}

.site-info a:hover {
    color: #2980b9;
}

.footer-navigation {
    text-align: right;
}

.footer-menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: flex-end;
    gap: 2rem;
}

.footer-menu li a {
    color: #bdc3c7;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-menu li a:hover {
    color: #3498db;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.back-to-top:hover {
    background: #2980b9;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

/* Responsive Footer */
@media (max-width: 768px) {
    .footer-widgets {
        padding: 2rem 0;
    }
    
    .footer-bottom .row {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-navigation {
        text-align: center;
        margin-top: 1rem;
    }
    
    .footer-menu {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
    }
}

/* Additional Content Styles */
.main-content {
    min-height: 60vh;
    padding: 2rem 0;
}

.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #ecf0f1;
}

.page-title {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.page-description {
    color: #7f8c8d;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Post Cards */
.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.post-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.post-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.post-content {
    padding: 1.5rem;
}

.entry-title {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.entry-title a {
    color: #2c3e50;
    text-decoration: none;
}

.entry-title a:hover {
    color: #3498db;
}

.entry-meta {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 1rem;
}

.entry-meta span {
    margin-right: 1rem;
}

.entry-summary {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.read-more {
    color: #3498db;
    text-decoration: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.read-more:hover {
    color: #2980b9;
}
</style>

</body>
</html>
