#!/usr/bin/env python3
"""
Generate a real PNG screenshot for VelocityShop WordPress theme
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_screenshot():
    # Create image with WordPress theme screenshot dimensions
    width, height = 1200, 900
    img = Image.new('RGB', (width, height), color='#f8f9fa')
    draw = ImageDraw.Draw(img)
    
    # Try to use a system font, fallback to default
    try:
        title_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 32)
        heading_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
        text_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 16)
        small_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
    except:
        title_font = ImageFont.load_default()
        heading_font = ImageFont.load_default()
        text_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # Header
    draw.rectangle([0, 0, width, 80], fill='#ffffff')
    draw.line([0, 78, width, 78], fill='#e9ecef', width=2)
    
    # Logo
    draw.rectangle([60, 25, 200, 55], fill='#3498db', outline='#3498db')
    draw.text((130, 35), 'VelocityShop', fill='white', font=heading_font, anchor='mm')
    
    # Navigation
    nav_items = ['Home', 'Shop', 'Categories', 'About', 'Contact']
    nav_x = 300
    for item in nav_items:
        color = '#3498db' if item == 'Home' else '#2c3e50'
        draw.text((nav_x, 40), item, fill=color, font=text_font, anchor='mm')
        if item == 'Home':
            draw.line([nav_x-20, 55, nav_x+20, 55], fill='#3498db', width=2)
        nav_x += 80
    
    # Header icons
    draw.ellipse([1050, 28, 1074, 52], fill='#3498db')
    draw.text((1062, 40), '🔍', fill='white', font=small_font, anchor='mm')
    
    draw.ellipse([1090, 28, 1114, 52], fill='#27ae60')
    draw.text((1102, 40), '👤', fill='white', font=small_font, anchor='mm')
    
    draw.ellipse([1130, 28, 1154, 52], fill='#e74c3c')
    draw.text((1142, 40), '🛒', fill='white', font=small_font, anchor='mm')
    
    # Cart count
    draw.ellipse([1148, 24, 1162, 38], fill='#c0392b')
    draw.text((1155, 31), '3', fill='white', font=small_font, anchor='mm')
    
    # Hero Section with gradient effect
    hero_height = 250
    for i in range(hero_height):
        # Create gradient effect
        ratio = i / hero_height
        r = int(102 + (118 - 102) * ratio)  # 667eea to 764ba2
        g = int(126 + (75 - 126) * ratio)
        b = int(234 + (162 - 234) * ratio)
        color = f'#{r:02x}{g:02x}{b:02x}'
        draw.line([0, 80 + i, width, 80 + i], fill=color)
    
    # Hero content
    draw.text((width//2, 150), 'Premium Store Experience', fill='white', font=title_font, anchor='mm')
    draw.text((width//2, 190), 'Discover amazing products with lightning-fast performance', fill='white', font=text_font, anchor='mm')
    
    # Hero button
    button_x, button_y = width//2 - 60, 220
    draw.rectangle([button_x, button_y, button_x + 120, button_y + 40], fill='#27ae60')
    draw.text((width//2, 240), 'SHOP NOW', fill='white', font=text_font, anchor='mm')
    
    # Products section title
    draw.text((width//2, 380), 'Featured Products', fill='#2c3e50', font=title_font, anchor='mm')
    
    # Product cards
    card_width, card_height = 250, 280
    card_spacing = 30
    start_x = (width - (4 * card_width + 3 * card_spacing)) // 2
    card_y = 420
    
    products = [
        {'name': 'Premium Smartphone', 'price': '$699.00', 'old_price': '$899.00', 'icon': '📱', 'sale': True},
        {'name': 'Modern Laptop', 'price': '$1,299.00', 'old_price': None, 'icon': '💻', 'sale': False},
        {'name': 'Wireless Headphones', 'price': '$199.00', 'old_price': None, 'icon': '🎧', 'sale': False},
        {'name': 'Smart Watch', 'price': '$399.00', 'old_price': None, 'icon': '⌚', 'sale': False}
    ]
    
    for i, product in enumerate(products):
        card_x = start_x + i * (card_width + card_spacing)
        
        # Card background
        draw.rectangle([card_x, card_y, card_x + card_width, card_y + card_height], 
                      fill='white', outline='#e9ecef', width=1)
        
        # Product image area
        img_height = 160
        draw.rectangle([card_x + 10, card_y + 10, card_x + card_width - 10, card_y + img_height], 
                      fill='#f8f9fa', outline='#e9ecef')
        
        # Product icon
        draw.text((card_x + card_width//2, card_y + img_height//2 + 10), product['icon'], 
                 font=title_font, anchor='mm')
        
        # Sale badge
        if product['sale']:
            draw.rectangle([card_x + 20, card_y + 20, card_x + 60, card_y + 40], fill='#e74c3c')
            draw.text((card_x + 40, card_y + 30), 'Sale!', fill='white', font=small_font, anchor='mm')
        
        # Product info
        info_y = card_y + img_height + 20
        
        # Product name
        draw.text((card_x + 20, info_y), product['name'], fill='#2c3e50', font=text_font)
        
        # Price
        price_y = info_y + 25
        if product['old_price']:
            draw.text((card_x + 20, price_y), product['old_price'], fill='#95a5a6', font=text_font)
            # Strike through effect
            text_bbox = draw.textbbox((card_x + 20, price_y), product['old_price'], font=text_font)
            draw.line([text_bbox[0], price_y + 8, text_bbox[2], price_y + 8], fill='#95a5a6', width=1)
            
            draw.text((card_x + 100, price_y), product['price'], fill='#27ae60', font=text_font)
        else:
            draw.text((card_x + 20, price_y), product['price'], fill='#27ae60', font=text_font)
        
        # Add to cart button
        button_y = price_y + 35
        draw.rectangle([card_x + 20, button_y, card_x + card_width - 20, button_y + 35], fill='#3498db')
        draw.text((card_x + card_width//2, button_y + 17), 'ADD TO CART', fill='white', font=small_font, anchor='mm')
    
    # Footer
    footer_y = height - 80
    draw.rectangle([0, footer_y, width, height], fill='#2c3e50')
    
    # Footer content
    draw.text((60, footer_y + 20), 'VelocityShop', fill='white', font=heading_font)
    draw.text((60, footer_y + 45), '© 2024 Premium WooCommerce Theme', fill='#bdc3c7', font=small_font)
    
    # Footer links
    footer_links = ['Privacy Policy', 'Terms of Service', 'Support', 'FAQ']
    link_x = 400
    for link in footer_links:
        draw.text((link_x, footer_y + 30), link, fill='#bdc3c7', font=small_font)
        link_x += 120
    
    # Social icons
    social_x = width - 150
    for i, icon in enumerate(['f', 't', 'i']):
        icon_x = social_x + i * 35
        draw.ellipse([icon_x, footer_y + 20, icon_x + 25, footer_y + 45], fill='#3498db')
        draw.text((icon_x + 12, footer_y + 32), icon, fill='white', font=text_font, anchor='mm')
    
    # Theme badge overlay
    badge_width, badge_height = 400, 120
    badge_x = (width - badge_width) // 2
    badge_y = (height - badge_height) // 2
    
    # Badge background with shadow effect
    shadow_offset = 5
    draw.rectangle([badge_x + shadow_offset, badge_y + shadow_offset,
                   badge_x + badge_width + shadow_offset, badge_y + badge_height + shadow_offset],
                  fill='#cccccc')

    draw.rectangle([badge_x, badge_y, badge_x + badge_width, badge_y + badge_height],
                  fill='white', outline='#3498db', width=3)
    
    # Badge content
    draw.text((badge_x + badge_width//2, badge_y + 30), 'VelocityShop', 
             fill='#2c3e50', font=title_font, anchor='mm')
    draw.text((badge_x + badge_width//2, badge_y + 60), 'Premium WooCommerce WordPress Theme', 
             fill='#7f8c8d', font=text_font, anchor='mm')
    
    # Feature badges
    features = ['⚡ Fast', '📱 Responsive', '🛒 WooCommerce']
    feature_x = badge_x + 50
    for feature in features:
        draw.text((feature_x, badge_y + 85), feature, fill='#27ae60', font=small_font)
        feature_x += 100
    
    return img

def main():
    print("Generating VelocityShop theme screenshot...")
    
    # Create the screenshot
    screenshot = create_screenshot()
    
    # Save as PNG
    screenshot.save('screenshot.png', 'PNG', quality=95)
    print("Screenshot saved as screenshot.png")
    
    # Also save as JPG for backup
    screenshot.save('screenshot.jpg', 'JPEG', quality=95)
    print("Screenshot also saved as screenshot.jpg")

if __name__ == "__main__":
    main()
