<?php
/**
 * VelocityShop Theme Functions
 * 
 * @package VelocityShop
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Theme version
define('VELOCITYSHOP_VERSION', '1.0.0');

/**
 * Theme Setup
 */
function velocityshop_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script'
    ));
    
    // Add WooCommerce support
    add_theme_support('woocommerce');
    add_theme_support('wc-product-gallery-zoom');
    add_theme_support('wc-product-gallery-lightbox');
    add_theme_support('wc-product-gallery-slider');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'velocityshop'),
        'footer' => __('Footer Menu', 'velocityshop'),
    ));
    
    // Add support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add support for editor styles
    add_theme_support('editor-styles');
    
    // Set content width
    $GLOBALS['content_width'] = 1200;
}
add_action('after_setup_theme', 'velocityshop_setup');

/**
 * Enqueue Scripts and Styles
 */
function velocityshop_scripts() {
    // Main stylesheet
    wp_enqueue_style(
        'velocityshop-style',
        get_stylesheet_uri(),
        array(),
        VELOCITYSHOP_VERSION
    );
    
    // Main JavaScript
    wp_enqueue_script(
        'velocityshop-main',
        get_template_directory_uri() . '/assets/js/main.js',
        array('jquery'),
        VELOCITYSHOP_VERSION,
        true
    );
    
    // Localize script for AJAX
    wp_localize_script('velocityshop-main', 'velocityshop_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('velocityshop_nonce'),
    ));
    
    // Comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'velocityshop_scripts');

/**
 * Register Widget Areas
 */
function velocityshop_widgets_init() {
    register_sidebar(array(
        'name'          => __('Main Sidebar', 'velocityshop'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here to appear in your sidebar.', 'velocityshop'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Shop Sidebar', 'velocityshop'),
        'id'            => 'shop-sidebar',
        'description'   => __('Add widgets here to appear in shop pages.', 'velocityshop'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area', 'velocityshop'),
        'id'            => 'footer-widgets',
        'description'   => __('Add widgets here to appear in the footer.', 'velocityshop'),
        'before_widget' => '<div class="footer-widget col-md-3"><section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section></div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'velocityshop_widgets_init');

/**
 * WooCommerce Customizations
 */

// Remove WooCommerce default styles
add_filter('woocommerce_enqueue_styles', '__return_empty_array');

// Change number of products per row
function velocityshop_loop_columns() {
    return 3;
}
add_filter('loop_shop_columns', 'velocityshop_loop_columns');

// Change number of products per page
function velocityshop_products_per_page() {
    return 12;
}
add_filter('loop_shop_per_page', 'velocityshop_products_per_page', 20);

// Remove WooCommerce breadcrumbs (we'll add our own)
remove_action('woocommerce_before_main_content', 'woocommerce_breadcrumb', 20);

// Customize WooCommerce wrapper
remove_action('woocommerce_before_main_content', 'woocommerce_output_content_wrapper', 10);
remove_action('woocommerce_after_main_content', 'woocommerce_output_content_wrapper_end', 10);

function velocityshop_wrapper_start() {
    echo '<div class="container"><div class="row"><main class="col-md-9">';
}
add_action('woocommerce_before_main_content', 'velocityshop_wrapper_start', 10);

function velocityshop_wrapper_end() {
    echo '</main>';
    get_sidebar('shop');
    echo '</div></div>';
}
add_action('woocommerce_after_main_content', 'velocityshop_wrapper_end', 10);

/**
 * Performance Optimizations
 */

// Remove unnecessary WordPress features for better performance
remove_action('wp_head', 'wp_generator');
remove_action('wp_head', 'wlwmanifest_link');
remove_action('wp_head', 'rsd_link');
remove_action('wp_head', 'wp_shortlink_wp_head');

// Disable emoji scripts
function velocityshop_disable_emojis() {
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_styles', 'print_emoji_styles');
    remove_filter('the_content_feed', 'wp_staticize_emoji');
    remove_filter('comment_text_rss', 'wp_staticize_emoji');
    remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
}
add_action('init', 'velocityshop_disable_emojis');

// Add lazy loading to images
function velocityshop_add_lazy_loading($content) {
    if (is_admin()) {
        return $content;
    }
    
    $content = preg_replace('/<img(.*?)src=/i', '<img$1loading="lazy" src=', $content);
    return $content;
}
add_filter('the_content', 'velocityshop_add_lazy_loading');

/**
 * Custom Functions
 */

// Get cart count for header
function velocityshop_cart_count() {
    if (function_exists('WC')) {
        return WC()->cart->get_cart_contents_count();
    }
    return 0;
}

// Custom breadcrumbs
function velocityshop_breadcrumbs() {
    if (!is_front_page()) {
        echo '<nav class="breadcrumbs" aria-label="Breadcrumb">';
        echo '<ol class="breadcrumb-list">';
        echo '<li><a href="' . home_url() . '">' . __('Home', 'velocityshop') . '</a></li>';
        
        if (is_shop()) {
            echo '<li>' . __('Shop', 'velocityshop') . '</li>';
        } elseif (is_product_category()) {
            $category = get_queried_object();
            echo '<li><a href="' . get_permalink(wc_get_page_id('shop')) . '">' . __('Shop', 'velocityshop') . '</a></li>';
            echo '<li>' . $category->name . '</li>';
        } elseif (is_product()) {
            echo '<li><a href="' . get_permalink(wc_get_page_id('shop')) . '">' . __('Shop', 'velocityshop') . '</a></li>';
            echo '<li>' . get_the_title() . '</li>';
        } elseif (is_page()) {
            echo '<li>' . get_the_title() . '</li>';
        } elseif (is_single()) {
            $category = get_the_category();
            if ($category) {
                echo '<li><a href="' . get_category_link($category[0]->term_id) . '">' . $category[0]->name . '</a></li>';
            }
            echo '<li>' . get_the_title() . '</li>';
        }
        
        echo '</ol>';
        echo '</nav>';
    }
}

/**
 * Include additional files
 */
require get_template_directory() . '/inc/customizer.php';
require get_template_directory() . '/inc/template-functions.php';
