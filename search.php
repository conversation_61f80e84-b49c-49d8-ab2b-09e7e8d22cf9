<?php
/**
 * The template for displaying search results pages
 *
 * @package VelocityShop
 * @version 1.0.0
 */

get_header(); ?>

<main class="site-main">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <header class="page-header">
                    <h1 class="page-title">
                        <?php
                        printf(
                            esc_html__('Search Results for: %s', 'velocityshop'),
                            '<span>' . get_search_query() . '</span>'
                        );
                        ?>
                    </h1>
                </header>

                <?php if (have_posts()) : ?>
                    <div class="search-results">
                        <?php while (have_posts()) : the_post(); ?>
                            <article id="post-<?php the_ID(); ?>" <?php post_class('search-result'); ?>>
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="post-thumbnail">
                                        <a href="<?php the_permalink(); ?>">
                                            <?php the_post_thumbnail('medium', array('loading' => 'lazy')); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="post-content">
                                    <header class="entry-header">
                                        <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>'); ?>
                                        
                                        <div class="entry-meta">
                                            <span class="post-type"><?php echo esc_html(get_post_type_object(get_post_type())->labels->singular_name); ?></span>
                                            <span class="posted-on">
                                                <time class="entry-date published" datetime="<?php echo esc_attr(get_the_date('c')); ?>">
                                                    <?php echo esc_html(get_the_date()); ?>
                                                </time>
                                            </span>
                                        </div>
                                    </header>

                                    <div class="entry-summary">
                                        <?php the_excerpt(); ?>
                                    </div>

                                    <footer class="entry-footer">
                                        <a href="<?php the_permalink(); ?>" class="read-more">
                                            <?php _e('Read More', 'velocityshop'); ?>
                                        </a>
                                    </footer>
                                </div>
                            </article>
                        <?php endwhile; ?>
                    </div>

                    <?php
                    the_posts_pagination(array(
                        'mid_size'  => 2,
                        'prev_text' => __('&laquo; Previous', 'velocityshop'),
                        'next_text' => __('Next &raquo;', 'velocityshop'),
                    ));
                    ?>

                <?php else : ?>
                    
                    <section class="no-results not-found">
                        <header class="page-header">
                            <h2 class="page-title"><?php _e('Nothing found', 'velocityshop'); ?></h2>
                        </header>

                        <div class="page-content">
                            <p><?php _e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'velocityshop'); ?></p>
                            
                            <?php get_search_form(); ?>
                            
                            <div class="search-suggestions">
                                <h3><?php _e('Search Suggestions', 'velocityshop'); ?></h3>
                                <ul>
                                    <li><?php _e('Check your spelling', 'velocityshop'); ?></li>
                                    <li><?php _e('Try more general keywords', 'velocityshop'); ?></li>
                                    <li><?php _e('Try different keywords', 'velocityshop'); ?></li>
                                    <li><?php _e('Try fewer keywords', 'velocityshop'); ?></li>
                                </ul>
                            </div>
                        </div>
                    </section>

                <?php endif; ?>
            </div>

            <aside class="col-md-4">
                <?php get_sidebar(); ?>
            </aside>
        </div>
    </div>
</main>

<style>
.search-results {
    margin-bottom: 3rem;
}

.search-result {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.search-result:last-child {
    border-bottom: none;
}

.search-result .post-thumbnail {
    flex-shrink: 0;
    width: 150px;
}

.search-result .post-thumbnail img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
}

.search-result .post-content {
    flex: 1;
}

.search-result .entry-title {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.search-result .entry-title a {
    color: #2c3e50;
    text-decoration: none;
}

.search-result .entry-title a:hover {
    color: #3498db;
}

.search-result .entry-meta {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 1rem;
}

.search-result .entry-meta span {
    margin-right: 1rem;
}

.search-result .post-type {
    background: #3498db;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: 600;
}

.search-result .entry-summary {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.search-result .read-more {
    color: #3498db;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-result .read-more:hover {
    color: #2980b9;
}

.search-suggestions {
    margin-top: 2rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.search-suggestions h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.search-suggestions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.search-suggestions li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.search-suggestions li:last-child {
    border-bottom: none;
}

.search-suggestions li:before {
    content: "💡";
    margin-right: 0.5rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .search-result {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-result .post-thumbnail {
        width: 100%;
    }
    
    .search-result .post-thumbnail img {
        height: 150px;
    }
}
</style>

<?php get_footer(); ?>
