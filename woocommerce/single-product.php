<?php
/**
 * The Template for displaying all single products
 *
 * @package VelocityShop
 * @version 1.0.0
 */

defined('ABSPATH') || exit;

get_header('shop'); ?>

<div class="single-product-wrapper">
    <div class="container">
        <div class="row">
            <main class="col-12">
                <?php
                /**
                 * woocommerce_before_main_content hook.
                 *
                 * @hooked woocommerce_output_content_wrapper - 10 (outputs opening divs for the content)
                 * @hooked woocommerce_breadcrumb - 20
                 */
                do_action('woocommerce_before_main_content');
                ?>

                <?php while (have_posts()) : ?>
                    <?php the_post(); ?>

                    <?php wc_get_template_part('content', 'single-product'); ?>

                <?php endwhile; // end of the loop. ?>

                <?php
                /**
                 * woocommerce_after_main_content hook.
                 *
                 * @hooked woocommerce_output_content_wrapper_end - 10 (outputs closing divs for the content)
                 */
                do_action('woocommerce_after_main_content');
                ?>
            </main>
        </div>
    </div>
</div>

<style>
.single-product-wrapper {
    padding: 2rem 0;
}

/* Product Gallery */
.woocommerce div.product .woocommerce-product-gallery {
    margin-bottom: 2rem;
}

.woocommerce div.product .woocommerce-product-gallery .woocommerce-product-gallery__wrapper {
    position: relative;
}

.woocommerce div.product .woocommerce-product-gallery .woocommerce-product-gallery__image {
    margin-bottom: 1rem;
}

.woocommerce div.product .woocommerce-product-gallery .flex-control-thumbs {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.woocommerce div.product .woocommerce-product-gallery .flex-control-thumbs li {
    list-style: none;
    margin: 0;
}

.woocommerce div.product .woocommerce-product-gallery .flex-control-thumbs li img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.woocommerce div.product .woocommerce-product-gallery .flex-control-thumbs li img:hover,
.woocommerce div.product .woocommerce-product-gallery .flex-control-thumbs li img.flex-active {
    opacity: 1;
}

/* Product Summary */
.woocommerce div.product .summary {
    position: relative;
}

.woocommerce div.product .summary .product_meta {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
    font-size: 0.9rem;
    color: #7f8c8d;
}

.woocommerce div.product .summary .product_meta > span {
    display: block;
    margin-bottom: 0.5rem;
}

/* Quantity Input */
.woocommerce div.product form.cart .quantity {
    margin-right: 1rem;
}

.woocommerce div.product form.cart .quantity .qty {
    width: 80px;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

/* Add to Cart Button */
.woocommerce div.product form.cart .single_add_to_cart_button {
    padding: 1rem 2rem;
    font-size: 1rem;
    min-width: 200px;
}

/* Product Tabs */
.woocommerce div.product .woocommerce-tabs {
    margin-top: 3rem;
}

.woocommerce div.product .woocommerce-tabs ul.tabs {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    border-bottom: 2px solid #eee;
    margin-bottom: 2rem;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li {
    margin: 0;
    margin-right: 2rem;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li a {
    display: block;
    padding: 1rem 0;
    text-decoration: none;
    color: #7f8c8d;
    font-weight: 600;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li.active a,
.woocommerce div.product .woocommerce-tabs ul.tabs li a:hover {
    color: #3498db;
    border-bottom-color: #3498db;
}

.woocommerce div.product .woocommerce-tabs .panel {
    padding: 2rem 0;
    line-height: 1.7;
}

/* Related Products */
.single-product .related.products {
    margin-top: 4rem;
    padding-top: 3rem;
    border-top: 2px solid #eee;
}

.single-product .related.products h2 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
    color: #2c3e50;
}

/* Reviews */
.woocommerce #reviews #comments {
    margin-bottom: 2rem;
}

.woocommerce #reviews #comments ol.commentlist {
    list-style: none;
    margin: 0;
    padding: 0;
}

.woocommerce #reviews #comments ol.commentlist li {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.woocommerce #reviews #comments ol.commentlist li .comment-text {
    margin-left: 0;
}

.woocommerce #reviews #comments ol.commentlist li .star-rating {
    margin-bottom: 0.5rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .woocommerce div.product {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .woocommerce div.product form.cart {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .woocommerce div.product form.cart .quantity {
        margin-right: 0;
        align-self: flex-start;
    }
    
    .woocommerce div.product .woocommerce-tabs ul.tabs {
        flex-direction: column;
        border-bottom: none;
    }
    
    .woocommerce div.product .woocommerce-tabs ul.tabs li {
        margin-right: 0;
        border-bottom: 1px solid #eee;
    }
    
    .woocommerce div.product .woocommerce-tabs ul.tabs li a {
        padding: 1rem;
        border-bottom: none;
    }
}
</style>

<?php
get_footer('shop');
?>
