<?php
/**
 * The Template for displaying product archives, including the main shop page
 *
 * @package VelocityShop
 * @version 1.0.0
 */

defined('ABSPATH') || exit;

get_header('shop'); ?>

<div class="shop-header">
    <div class="container">
        <?php if (apply_filters('woocommerce_show_page_title', true)) : ?>
            <h1 class="woocommerce-products-header__title page-title"><?php woocommerce_page_title(); ?></h1>
        <?php endif; ?>

        <?php
        /**
         * Hook: woocommerce_archive_description.
         *
         * @hooked woocommerce_taxonomy_archive_description - 10
         * @hooked woocommerce_product_archive_description - 10
         */
        do_action('woocommerce_archive_description');
        ?>
    </div>
</div>

<div class="shop-content">
    <div class="container">
        <div class="row">
            <main class="col-md-9">
                <?php
                if (woocommerce_product_loop()) {

                    /**
                     * Hook: woocommerce_before_shop_loop.
                     *
                     * @hooked woocommerce_output_all_notices - 10
                     * @hooked woocommerce_result_count - 20
                     * @hooked woocommerce_catalog_ordering - 30
                     */
                    do_action('woocommerce_before_shop_loop');

                    woocommerce_product_loop_start();

                    if (wc_get_loop_prop('is_shortcode')) {
                        $columns = absint(wc_get_loop_prop('columns'));
                    } else {
                        $columns = wc_get_default_products_per_row();
                    }

                    while (have_posts()) {
                        the_post();

                        /**
                         * Hook: woocommerce_shop_loop.
                         */
                        do_action('woocommerce_shop_loop');

                        wc_get_template_part('content', 'product');
                    }

                    woocommerce_product_loop_end();

                    /**
                     * Hook: woocommerce_after_shop_loop.
                     *
                     * @hooked woocommerce_pagination - 10
                     */
                    do_action('woocommerce_after_shop_loop');
                } else {
                    /**
                     * Hook: woocommerce_no_products_found.
                     *
                     * @hooked wc_no_products_found - 10
                     */
                    do_action('woocommerce_no_products_found');
                }
                ?>
            </main>

            <?php get_sidebar('shop'); ?>
        </div>
    </div>
</div>

<style>
.shop-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
}

.shop-header .page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: white;
}

.woocommerce-products-header__description {
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.6;
}

.shop-content {
    padding: 2rem 0;
}

.woocommerce-result-count,
.woocommerce-ordering {
    margin-bottom: 2rem;
}

.woocommerce-result-count {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.woocommerce-ordering select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

/* Product Grid Enhancements */
.woocommerce ul.products {
    margin-bottom: 3rem;
}

.woocommerce ul.products li.product {
    position: relative;
}

.woocommerce ul.products li.product .onsale {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #e74c3c;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.woocommerce ul.products li.product .woocommerce-loop-product__link:hover .woocommerce-loop-product__title {
    color: #3498db;
}

/* Pagination */
.woocommerce nav.woocommerce-pagination {
    text-align: center;
    margin-top: 3rem;
}

.woocommerce nav.woocommerce-pagination ul {
    display: inline-flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
}

.woocommerce nav.woocommerce-pagination ul li {
    margin: 0;
}

.woocommerce nav.woocommerce-pagination ul li a,
.woocommerce nav.woocommerce-pagination ul li span {
    display: block;
    padding: 0.75rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.woocommerce nav.woocommerce-pagination ul li a:hover,
.woocommerce nav.woocommerce-pagination ul li span.current {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* No Products Found */
.woocommerce .woocommerce-info {
    text-align: center;
    padding: 3rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 2rem 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .shop-header {
        padding: 2rem 0;
    }
    
    .shop-header .page-title {
        font-size: 2rem;
    }
    
    .woocommerce ul.products {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .woocommerce-result-count,
    .woocommerce-ordering {
        text-align: center;
        margin-bottom: 1rem;
    }
}
</style>

<?php
get_footer('shop');
?>
