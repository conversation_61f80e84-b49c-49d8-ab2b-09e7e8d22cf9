<?php
/**
 * Checkout Form
 *
 * @package VelocityShop
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

do_action('woocommerce_before_checkout_form', $checkout);

// If checkout registration is disabled and not logged in, the user cannot checkout.
if (!$checkout->is_registration_enabled() && $checkout->is_registration_required() && !is_user_logged_in()) {
    echo esc_html(apply_filters('woocommerce_checkout_must_be_logged_in_message', __('You must be logged in to checkout.', 'woocommerce')));
    return;
}
?>

<div class="checkout-wrapper">
    <div class="container">
        <form name="checkout" method="post" class="checkout woocommerce-checkout" action="<?php echo esc_url(wc_get_checkout_url()); ?>" enctype="multipart/form-data">

            <?php if ($checkout->get_checkout_fields()) : ?>

                <?php do_action('woocommerce_checkout_before_customer_details'); ?>

                <div class="checkout-form-wrapper" id="customer_details">
                    <div class="checkout-billing">
                        <div class="woocommerce-billing-fields">
                            <?php if (wc_ship_to_billing_address_only() && $checkout->get_checkout_fields('shipping')) : ?>
                                <h3><?php esc_html_e('Billing &amp; Shipping', 'woocommerce'); ?></h3>
                            <?php else : ?>
                                <h3><?php esc_html_e('Billing details', 'woocommerce'); ?></h3>
                            <?php endif; ?>

                            <?php do_action('woocommerce_checkout_billing'); ?>
                        </div>
                    </div>

                    <div class="checkout-shipping">
                        <?php if (!wc_ship_to_billing_address_only() && $checkout->get_checkout_fields('shipping')) : ?>
                            <div class="woocommerce-shipping-fields">
                                <?php if (WC()->cart->needs_shipping_address()) : ?>
                                    <h3 id="ship-to-different-address">
                                        <label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
                                            <input id="ship-to-different-address-checkbox" class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" <?php checked(apply_filters('woocommerce_ship_to_different_address_checked', 'shipping' === get_option('woocommerce_ship_to_destination') ? 1 : 0), 1); ?> type="checkbox" name="ship_to_different_address" value="1" />
                                            <span><?php esc_html_e('Ship to a different address?', 'woocommerce'); ?></span>
                                        </label>
                                    </h3>

                                    <div class="shipping_address">
                                        <?php do_action('woocommerce_checkout_shipping'); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <?php do_action('woocommerce_checkout_after_customer_details'); ?>

                        <?php if (isset($checkout->checkout_fields['order']) && !empty($checkout->checkout_fields['order'])) : ?>
                            <div class="woocommerce-additional-fields">
                                <h3><?php esc_html_e('Additional information', 'woocommerce'); ?></h3>
                                <div class="woocommerce-additional-fields__field-wrapper">
                                    <?php foreach ($checkout->get_checkout_fields('order') as $key => $field) : ?>
                                        <?php woocommerce_form_field($key, $field, $checkout->get_value($key)); ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            <?php endif; ?>

            <?php do_action('woocommerce_checkout_before_order_review_heading'); ?>

            <h3 id="order_review_heading"><?php esc_html_e('Your order', 'woocommerce'); ?></h3>

            <?php do_action('woocommerce_checkout_before_order_review'); ?>

            <div id="order_review" class="woocommerce-checkout-review-order">
                <?php do_action('woocommerce_checkout_order_review'); ?>
            </div>

            <?php do_action('woocommerce_checkout_after_order_review'); ?>

        </form>
    </div>
</div>

<?php do_action('woocommerce_after_checkout_form', $checkout); ?>

<style>
.checkout-wrapper {
    padding: 2rem 0;
}

.checkout-form-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.checkout-billing,
.checkout-shipping {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
}

.checkout-billing h3,
.checkout-shipping h3 {
    margin-bottom: 1.5rem;
    color: #2c3e50;
    font-size: 1.3rem;
}

.woocommerce-checkout-review-order {
    background: #fff;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 2rem;
}

.woocommerce-checkout-review-order h3 {
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

/* Form Fields */
.woocommerce-checkout .form-row {
    margin-bottom: 1.5rem;
}

.woocommerce-checkout .form-row label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.woocommerce-checkout .form-row input[type="text"],
.woocommerce-checkout .form-row input[type="email"],
.woocommerce-checkout .form-row input[type="tel"],
.woocommerce-checkout .form-row select,
.woocommerce-checkout .form-row textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.woocommerce-checkout .form-row input:focus,
.woocommerce-checkout .form-row select:focus,
.woocommerce-checkout .form-row textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.woocommerce-checkout .form-row-first,
.woocommerce-checkout .form-row-last {
    width: 48%;
    float: left;
}

.woocommerce-checkout .form-row-last {
    float: right;
}

.woocommerce-checkout .form-row-wide {
    clear: both;
}

/* Checkbox Styling */
.woocommerce-checkout .checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.woocommerce-checkout .checkbox input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Ship to Different Address */
#ship-to-different-address {
    margin-bottom: 1rem;
}

.shipping_address {
    display: none;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.shipping_address.active {
    display: block;
}

/* Order Review Table */
.woocommerce-checkout-review-order-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
}

.woocommerce-checkout-review-order-table th,
.woocommerce-checkout-review-order-table td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    text-align: left;
}

.woocommerce-checkout-review-order-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.woocommerce-checkout-review-order-table .product-thumbnail img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
}

/* Payment Methods */
.woocommerce-checkout #payment {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    margin-top: 2rem;
}

.woocommerce-checkout #payment ul.payment_methods {
    list-style: none;
    margin: 0;
    padding: 0;
}

.woocommerce-checkout #payment ul.payment_methods li {
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 4px;
    border: 1px solid #eee;
}

.woocommerce-checkout #payment ul.payment_methods li.wc_payment_method input {
    margin-right: 0.5rem;
}

.woocommerce-checkout #payment .payment_box {
    background: #fff;
    padding: 1rem;
    margin-top: 1rem;
    border-radius: 4px;
    border: 1px solid #eee;
}

/* Place Order Button */
.woocommerce-checkout #place_order {
    width: 100%;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.woocommerce-checkout #place_order:hover {
    background: #229954;
    transform: translateY(-2px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .checkout-form-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .woocommerce-checkout .form-row-first,
    .woocommerce-checkout .form-row-last {
        width: 100%;
        float: none;
    }
    
    .checkout-billing,
    .checkout-shipping {
        padding: 1.5rem;
    }
    
    .woocommerce-checkout-review-order {
        padding: 1.5rem;
    }
}
</style>
