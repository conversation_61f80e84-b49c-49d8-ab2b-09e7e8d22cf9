<?php
/**
 * The header for our theme
 *
 * @package VelocityShop
 * @version 1.0.0
 */
?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="<?php echo get_stylesheet_uri(); ?>" as="style">
    <link rel="preload" href="<?php echo get_template_directory_uri(); ?>/assets/js/main.js" as="script">
    
    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#primary"><?php _e('Skip to content', 'velocityshop'); ?></a>

    <header id="masthead" class="site-header">
        <div class="container">
            <div class="header-container">
                <!-- Site Logo/Branding -->
                <div class="site-branding">
                    <?php
                    if (has_custom_logo()) :
                        the_custom_logo();
                    else :
                    ?>
                        <h1 class="site-logo">
                            <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                                <?php bloginfo('name'); ?>
                            </a>
                        </h1>
                        <?php
                        $description = get_bloginfo('description', 'display');
                        if ($description || is_customize_preview()) :
                        ?>
                            <p class="site-description"><?php echo $description; ?></p>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>

                <!-- Main Navigation -->
                <nav id="site-navigation" class="main-navigation" role="navigation" aria-label="<?php _e('Primary Menu', 'velocityshop'); ?>">
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_id'        => 'primary-menu',
                        'menu_class'     => 'nav-menu',
                        'container'      => false,
                        'fallback_cb'    => 'velocityshop_fallback_menu',
                    ));
                    ?>
                </nav>

                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Search Toggle -->
                    <button class="search-toggle" aria-label="<?php _e('Search', 'velocityshop'); ?>" aria-expanded="false">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </button>

                    <!-- User Account -->
                    <?php if (function_exists('wc_get_account_endpoint_url')) : ?>
                        <a href="<?php echo esc_url(wc_get_account_endpoint_url('dashboard')); ?>" class="account-toggle" aria-label="<?php _e('My Account', 'velocityshop'); ?>">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </a>
                    <?php endif; ?>

                    <!-- Shopping Cart -->
                    <?php if (function_exists('wc_get_cart_url')) : ?>
                        <a href="<?php echo esc_url(wc_get_cart_url()); ?>" class="cart-toggle" aria-label="<?php _e('Shopping Cart', 'velocityshop'); ?>">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                            </svg>
                            <?php
                            $cart_count = velocityshop_cart_count();
                            if ($cart_count > 0) :
                            ?>
                                <span class="cart-count"><?php echo esc_html($cart_count); ?></span>
                            <?php endif; ?>
                        </a>
                    <?php endif; ?>

                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" aria-label="<?php _e('Menu', 'velocityshop'); ?>" aria-expanded="false">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Search Form (Hidden by default) -->
        <div class="header-search" style="display: none;">
            <div class="container">
                <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                    <label for="search-field" class="screen-reader-text"><?php _e('Search for:', 'velocityshop'); ?></label>
                    <input type="search" id="search-field" class="search-field" placeholder="<?php _e('Search products...', 'velocityshop'); ?>" value="<?php echo get_search_query(); ?>" name="s" />
                    <?php if (function_exists('wc_get_page_id')) : ?>
                        <input type="hidden" name="post_type" value="product" />
                    <?php endif; ?>
                    <button type="submit" class="search-submit">
                        <span class="screen-reader-text"><?php _e('Search', 'velocityshop'); ?></span>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </header>

    <?php
    // Add breadcrumbs for non-front pages
    if (!is_front_page()) {
        echo '<div class="container">';
        velocityshop_breadcrumbs();
        echo '</div>';
    }
    ?>

<?php
/**
 * Fallback menu function
 */
function velocityshop_fallback_menu() {
    echo '<ul class="nav-menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">' . __('Home', 'velocityshop') . '</a></li>';
    if (function_exists('wc_get_page_id')) {
        echo '<li><a href="' . esc_url(get_permalink(wc_get_page_id('shop'))) . '">' . __('Shop', 'velocityshop') . '</a></li>';
    }
    echo '<li><a href="' . esc_url(get_permalink(get_option('page_for_posts'))) . '">' . __('Blog', 'velocityshop') . '</a></li>';
    echo '</ul>';
}
?>
