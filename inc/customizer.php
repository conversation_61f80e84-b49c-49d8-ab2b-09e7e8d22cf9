<?php
/**
 * VelocityShop Theme Customizer
 *
 * @package VelocityShop
 * @version 1.0.0
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 *
 * @param WP_Customize_Manager $wp_customize Theme Customizer object.
 */
function velocityshop_customize_register($wp_customize) {
    $wp_customize->get_setting('blogname')->transport         = 'postMessage';
    $wp_customize->get_setting('blogdescription')->transport  = 'postMessage';
    $wp_customize->get_setting('header_textcolor')->transport = 'postMessage';

    if (isset($wp_customize->selective_refresh)) {
        $wp_customize->selective_refresh->add_partial(
            'blogname',
            array(
                'selector'        => '.site-title a',
                'render_callback' => 'velocityshop_customize_partial_blogname',
            )
        );
        $wp_customize->selective_refresh->add_partial(
            'blogdescription',
            array(
                'selector'        => '.site-description',
                'render_callback' => 'velocityshop_customize_partial_blogdescription',
            )
        );
    }

    // Theme Colors Section
    $wp_customize->add_section('velocityshop_colors', array(
        'title'    => __('Theme Colors', 'velocityshop'),
        'priority' => 30,
    ));

    // Primary Color
    $wp_customize->add_setting('velocityshop_primary_color', array(
        'default'           => '#3498db',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'velocityshop_primary_color', array(
        'label'    => __('Primary Color', 'velocityshop'),
        'section'  => 'velocityshop_colors',
        'settings' => 'velocityshop_primary_color',
    )));

    // Secondary Color
    $wp_customize->add_setting('velocityshop_secondary_color', array(
        'default'           => '#2c3e50',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'velocityshop_secondary_color', array(
        'label'    => __('Secondary Color', 'velocityshop'),
        'section'  => 'velocityshop_colors',
        'settings' => 'velocityshop_secondary_color',
    )));

    // Accent Color
    $wp_customize->add_setting('velocityshop_accent_color', array(
        'default'           => '#27ae60',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'velocityshop_accent_color', array(
        'label'    => __('Accent Color', 'velocityshop'),
        'section'  => 'velocityshop_colors',
        'settings' => 'velocityshop_accent_color',
    )));

    // Typography Section
    $wp_customize->add_section('velocityshop_typography', array(
        'title'    => __('Typography', 'velocityshop'),
        'priority' => 35,
    ));

    // Body Font
    $wp_customize->add_setting('velocityshop_body_font', array(
        'default'           => 'system',
        'sanitize_callback' => 'velocityshop_sanitize_font',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control('velocityshop_body_font', array(
        'label'    => __('Body Font', 'velocityshop'),
        'section'  => 'velocityshop_typography',
        'type'     => 'select',
        'choices'  => array(
            'system'     => __('System Font', 'velocityshop'),
            'roboto'     => __('Roboto', 'velocityshop'),
            'open-sans'  => __('Open Sans', 'velocityshop'),
            'lato'       => __('Lato', 'velocityshop'),
            'montserrat' => __('Montserrat', 'velocityshop'),
        ),
    ));

    // Heading Font
    $wp_customize->add_setting('velocityshop_heading_font', array(
        'default'           => 'system',
        'sanitize_callback' => 'velocityshop_sanitize_font',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control('velocityshop_heading_font', array(
        'label'    => __('Heading Font', 'velocityshop'),
        'section'  => 'velocityshop_typography',
        'type'     => 'select',
        'choices'  => array(
            'system'     => __('System Font', 'velocityshop'),
            'roboto'     => __('Roboto', 'velocityshop'),
            'open-sans'  => __('Open Sans', 'velocityshop'),
            'lato'       => __('Lato', 'velocityshop'),
            'montserrat' => __('Montserrat', 'velocityshop'),
            'playfair'   => __('Playfair Display', 'velocityshop'),
        ),
    ));

    // Layout Section
    $wp_customize->add_section('velocityshop_layout', array(
        'title'    => __('Layout Options', 'velocityshop'),
        'priority' => 40,
    ));

    // Container Width
    $wp_customize->add_setting('velocityshop_container_width', array(
        'default'           => '1200',
        'sanitize_callback' => 'absint',
        'transport'         => 'postMessage',
    ));

    $wp_customize->add_control('velocityshop_container_width', array(
        'label'       => __('Container Width (px)', 'velocityshop'),
        'section'     => 'velocityshop_layout',
        'type'        => 'number',
        'input_attrs' => array(
            'min'  => 960,
            'max'  => 1400,
            'step' => 20,
        ),
    ));

    // Sidebar Position
    $wp_customize->add_setting('velocityshop_sidebar_position', array(
        'default'           => 'right',
        'sanitize_callback' => 'velocityshop_sanitize_sidebar_position',
    ));

    $wp_customize->add_control('velocityshop_sidebar_position', array(
        'label'   => __('Sidebar Position', 'velocityshop'),
        'section' => 'velocityshop_layout',
        'type'    => 'radio',
        'choices' => array(
            'left'  => __('Left', 'velocityshop'),
            'right' => __('Right', 'velocityshop'),
            'none'  => __('No Sidebar', 'velocityshop'),
        ),
    ));

    // WooCommerce Section
    if (class_exists('WooCommerce')) {
        $wp_customize->add_section('velocityshop_woocommerce', array(
            'title'    => __('WooCommerce Settings', 'velocityshop'),
            'priority' => 45,
        ));

        // Products per row
        $wp_customize->add_setting('velocityshop_products_per_row', array(
            'default'           => '3',
            'sanitize_callback' => 'absint',
        ));

        $wp_customize->add_control('velocityshop_products_per_row', array(
            'label'   => __('Products per Row', 'velocityshop'),
            'section' => 'velocityshop_woocommerce',
            'type'    => 'select',
            'choices' => array(
                '2' => __('2 Columns', 'velocityshop'),
                '3' => __('3 Columns', 'velocityshop'),
                '4' => __('4 Columns', 'velocityshop'),
            ),
        ));

        // Products per page
        $wp_customize->add_setting('velocityshop_products_per_page', array(
            'default'           => '12',
            'sanitize_callback' => 'absint',
        ));

        $wp_customize->add_control('velocityshop_products_per_page', array(
            'label'       => __('Products per Page', 'velocityshop'),
            'section'     => 'velocityshop_woocommerce',
            'type'        => 'number',
            'input_attrs' => array(
                'min'  => 6,
                'max'  => 48,
                'step' => 6,
            ),
        ));

        // Show sale badge
        $wp_customize->add_setting('velocityshop_show_sale_badge', array(
            'default'           => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ));

        $wp_customize->add_control('velocityshop_show_sale_badge', array(
            'label'   => __('Show Sale Badge', 'velocityshop'),
            'section' => 'velocityshop_woocommerce',
            'type'    => 'checkbox',
        ));
    }

    // Performance Section
    $wp_customize->add_section('velocityshop_performance', array(
        'title'    => __('Performance', 'velocityshop'),
        'priority' => 50,
    ));

    // Enable lazy loading
    $wp_customize->add_setting('velocityshop_lazy_loading', array(
        'default'           => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));

    $wp_customize->add_control('velocityshop_lazy_loading', array(
        'label'   => __('Enable Lazy Loading', 'velocityshop'),
        'section' => 'velocityshop_performance',
        'type'    => 'checkbox',
    ));

    // Minify CSS
    $wp_customize->add_setting('velocityshop_minify_css', array(
        'default'           => false,
        'sanitize_callback' => 'wp_validate_boolean',
    ));

    $wp_customize->add_control('velocityshop_minify_css', array(
        'label'   => __('Minify CSS', 'velocityshop'),
        'section' => 'velocityshop_performance',
        'type'    => 'checkbox',
    ));
}
add_action('customize_register', 'velocityshop_customize_register');

/**
 * Render the site title for the selective refresh partial.
 *
 * @return void
 */
function velocityshop_customize_partial_blogname() {
    bloginfo('name');
}

/**
 * Render the site tagline for the selective refresh partial.
 *
 * @return void
 */
function velocityshop_customize_partial_blogdescription() {
    bloginfo('description');
}

/**
 * Sanitize font choices.
 */
function velocityshop_sanitize_font($input) {
    $valid = array('system', 'roboto', 'open-sans', 'lato', 'montserrat', 'playfair');
    return in_array($input, $valid) ? $input : 'system';
}

/**
 * Sanitize sidebar position.
 */
function velocityshop_sanitize_sidebar_position($input) {
    $valid = array('left', 'right', 'none');
    return in_array($input, $valid) ? $input : 'right';
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function velocityshop_customize_preview_js() {
    wp_enqueue_script(
        'velocityshop-customizer',
        get_template_directory_uri() . '/assets/js/customizer.js',
        array('customize-preview'),
        VELOCITYSHOP_VERSION,
        true
    );
}
add_action('customize_preview_init', 'velocityshop_customize_preview_js');

/**
 * Output customizer styles
 */
function velocityshop_customizer_styles() {
    $primary_color = get_theme_mod('velocityshop_primary_color', '#3498db');
    $secondary_color = get_theme_mod('velocityshop_secondary_color', '#2c3e50');
    $accent_color = get_theme_mod('velocityshop_accent_color', '#27ae60');
    $container_width = get_theme_mod('velocityshop_container_width', '1200');
    $body_font = get_theme_mod('velocityshop_body_font', 'system');
    $heading_font = get_theme_mod('velocityshop_heading_font', 'system');

    $css = "
    :root {
        --primary-color: {$primary_color};
        --secondary-color: {$secondary_color};
        --accent-color: {$accent_color};
        --container-width: {$container_width}px;
    }
    
    .container {
        max-width: {$container_width}px;
    }
    
    a, .nav-menu a:hover, .woocommerce .button {
        color: {$primary_color};
    }
    
    .woocommerce .button, .woocommerce button.button, .woocommerce input.button {
        background: {$primary_color};
    }
    
    .woocommerce .button.alt {
        background: {$accent_color};
    }
    
    h1, h2, h3, h4, h5, h6 {
        color: {$secondary_color};
    }
    ";

    // Add font imports and styles
    if ($body_font !== 'system' || $heading_font !== 'system') {
        $fonts_to_load = array();
        
        if ($body_font !== 'system') {
            $fonts_to_load[] = velocityshop_get_font_url($body_font);
            $css .= "body { font-family: " . velocityshop_get_font_family($body_font) . "; }";
        }
        
        if ($heading_font !== 'system' && $heading_font !== $body_font) {
            $fonts_to_load[] = velocityshop_get_font_url($heading_font);
        }
        
        if ($heading_font !== 'system') {
            $css .= "h1, h2, h3, h4, h5, h6 { font-family: " . velocityshop_get_font_family($heading_font) . "; }";
        }
        
        // Enqueue Google Fonts
        foreach (array_unique($fonts_to_load) as $font_url) {
            wp_enqueue_style('velocityshop-google-font-' . md5($font_url), $font_url);
        }
    }

    wp_add_inline_style('velocityshop-style', $css);
}
add_action('wp_enqueue_scripts', 'velocityshop_customizer_styles');

/**
 * Get Google Font URL
 */
function velocityshop_get_font_url($font) {
    $fonts = array(
        'roboto'     => 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap',
        'open-sans'  => 'https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap',
        'lato'       => 'https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap',
        'montserrat' => 'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap',
        'playfair'   => 'https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap',
    );
    
    return isset($fonts[$font]) ? $fonts[$font] : '';
}

/**
 * Get Font Family CSS
 */
function velocityshop_get_font_family($font) {
    $families = array(
        'roboto'     => '"Roboto", sans-serif',
        'open-sans'  => '"Open Sans", sans-serif',
        'lato'       => '"Lato", sans-serif',
        'montserrat' => '"Montserrat", sans-serif',
        'playfair'   => '"Playfair Display", serif',
    );
    
    return isset($families[$font]) ? $families[$font] : 'inherit';
}
