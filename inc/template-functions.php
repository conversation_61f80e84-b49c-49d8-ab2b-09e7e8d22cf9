<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package VelocityShop
 * @version 1.0.0
 */

/**
 * Adds custom classes to the array of body classes.
 *
 * @param array $classes Classes for the body element.
 * @return array
 */
function velocityshop_body_classes($classes) {
    // Adds a class of hfeed to non-singular pages.
    if (!is_singular()) {
        $classes[] = 'hfeed';
    }

    // Adds a class of no-sidebar when there is no sidebar present.
    if (!is_active_sidebar('sidebar-1')) {
        $classes[] = 'no-sidebar';
    }

    // Add WooCommerce specific classes
    if (function_exists('is_woocommerce')) {
        if (is_woocommerce()) {
            $classes[] = 'woocommerce-page';
        }
        
        if (is_shop()) {
            $classes[] = 'shop-page';
        }
        
        if (is_product()) {
            $classes[] = 'single-product-page';
        }
        
        if (is_cart()) {
            $classes[] = 'cart-page';
        }
        
        if (is_checkout()) {
            $classes[] = 'checkout-page';
        }
    }

    // Add sidebar position class
    $sidebar_position = get_theme_mod('velocityshop_sidebar_position', 'right');
    $classes[] = 'sidebar-' . $sidebar_position;

    return $classes;
}
add_filter('body_class', 'velocityshop_body_classes');

/**
 * Add a pingback url auto-discovery header for single posts, pages, or attachments.
 */
function velocityshop_pingback_header() {
    if (is_singular() && pings_open()) {
        printf('<link rel="pingback" href="%s">', esc_url(get_bloginfo('pingback_url')));
    }
}
add_action('wp_head', 'velocityshop_pingback_header');

/**
 * Custom excerpt length
 */
function velocityshop_excerpt_length($length) {
    return 25;
}
add_filter('excerpt_length', 'velocityshop_excerpt_length');

/**
 * Custom excerpt more
 */
function velocityshop_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'velocityshop_excerpt_more');

/**
 * Custom search form
 */
function velocityshop_search_form($form) {
    $form = '<form role="search" method="get" class="search-form" action="' . home_url('/') . '">
        <label>
            <span class="screen-reader-text">' . _x('Search for:', 'label', 'velocityshop') . '</span>
            <input type="search" class="search-field" placeholder="' . esc_attr_x('Search...', 'placeholder', 'velocityshop') . '" value="' . get_search_query() . '" name="s" />
        </label>
        <button type="submit" class="search-submit">
            <span class="screen-reader-text">' . _x('Search', 'submit button', 'velocityshop') . '</span>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>
        </button>
    </form>';

    return $form;
}
add_filter('get_search_form', 'velocityshop_search_form');

/**
 * AJAX handler for cart count
 */
function velocityshop_get_cart_count() {
    check_ajax_referer('velocityshop_nonce', 'nonce');
    
    $count = 0;
    if (function_exists('WC')) {
        $count = WC()->cart->get_cart_contents_count();
    }
    
    wp_send_json_success(array('count' => $count));
}
add_action('wp_ajax_get_cart_count', 'velocityshop_get_cart_count');
add_action('wp_ajax_nopriv_get_cart_count', 'velocityshop_get_cart_count');

/**
 * Optimize WooCommerce scripts loading
 */
function velocityshop_optimize_woocommerce_scripts() {
    // Remove WooCommerce scripts on non-WooCommerce pages
    if (function_exists('is_woocommerce')) {
        if (!is_woocommerce() && !is_cart() && !is_checkout() && !is_account_page()) {
            // Dequeue WooCommerce styles
            wp_dequeue_style('woocommerce-general');
            wp_dequeue_style('woocommerce-layout');
            wp_dequeue_style('woocommerce-smallscreen');
            
            // Dequeue WooCommerce scripts
            wp_dequeue_script('wc-cart-fragments');
            wp_dequeue_script('woocommerce');
            wp_dequeue_script('wc-add-to-cart');
        }
    }
}
add_action('wp_enqueue_scripts', 'velocityshop_optimize_woocommerce_scripts', 99);

/**
 * Add structured data for products
 */
function velocityshop_product_structured_data() {
    if (is_product()) {
        global $product;
        
        $structured_data = array(
            '@context' => 'https://schema.org',
            '@type' => 'Product',
            'name' => $product->get_name(),
            'description' => wp_strip_all_tags($product->get_short_description()),
            'sku' => $product->get_sku(),
            'offers' => array(
                '@type' => 'Offer',
                'price' => $product->get_price(),
                'priceCurrency' => get_woocommerce_currency(),
                'availability' => $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
                'url' => get_permalink($product->get_id())
            )
        );
        
        if ($product->get_image_id()) {
            $image = wp_get_attachment_image_src($product->get_image_id(), 'full');
            $structured_data['image'] = $image[0];
        }
        
        if ($product->get_average_rating()) {
            $structured_data['aggregateRating'] = array(
                '@type' => 'AggregateRating',
                'ratingValue' => $product->get_average_rating(),
                'reviewCount' => $product->get_review_count()
            );
        }
        
        echo '<script type="application/ld+json">' . json_encode($structured_data) . '</script>';
    }
}
add_action('wp_head', 'velocityshop_product_structured_data');

/**
 * Add preload hints for critical resources
 */
function velocityshop_preload_hints() {
    // Preload critical CSS
    echo '<link rel="preload" href="' . get_stylesheet_uri() . '" as="style">';
    
    // Preload critical JavaScript
    echo '<link rel="preload" href="' . get_template_directory_uri() . '/assets/js/main.js" as="script">';
    
    // DNS prefetch for external resources
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">';
    echo '<link rel="dns-prefetch" href="//fonts.gstatic.com">';
}
add_action('wp_head', 'velocityshop_preload_hints', 1);

/**
 * Add critical CSS inline
 */
function velocityshop_critical_css() {
    $critical_css = '
    .site-header{background:#fff;box-shadow:0 2px 10px rgba(0,0,0,0.1);position:sticky;top:0;z-index:1000}
    .container{max-width:1200px;margin:0 auto;padding:0 20px}
    .header-container{display:flex;align-items:center;justify-content:space-between;padding:1rem 0}
    .site-logo{font-size:1.8rem;font-weight:700;color:#2c3e50}
    .nav-menu{display:flex;list-style:none;margin:0;padding:0}
    .nav-menu li{margin:0 1rem}
    .nav-menu a{color:#333;font-weight:500;text-decoration:none}
    body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,sans-serif;margin:0;padding:0}
    ';
    
    echo '<style id="velocityshop-critical-css">' . $critical_css . '</style>';
}
add_action('wp_head', 'velocityshop_critical_css', 2);

/**
 * Defer non-critical CSS
 */
function velocityshop_defer_css($html, $handle) {
    if ('velocityshop-style' === $handle) {
        $html = str_replace("rel='stylesheet'", "rel='preload' as='style' onload=\"this.onload=null;this.rel='stylesheet'\"", $html);
        $html .= '<noscript><link rel="stylesheet" href="' . get_stylesheet_uri() . '"></noscript>';
    }
    return $html;
}
// Uncomment to enable CSS deferring (may cause FOUC)
// add_filter('style_loader_tag', 'velocityshop_defer_css', 10, 2);

/**
 * Add async/defer to scripts
 */
function velocityshop_script_attributes($tag, $handle) {
    $async_scripts = array('velocityshop-main');
    $defer_scripts = array('comment-reply');
    
    if (in_array($handle, $async_scripts)) {
        $tag = str_replace(' src', ' async src', $tag);
    }
    
    if (in_array($handle, $defer_scripts)) {
        $tag = str_replace(' src', ' defer src', $tag);
    }
    
    return $tag;
}
add_filter('script_loader_tag', 'velocityshop_script_attributes', 10, 2);

/**
 * Remove query strings from static resources
 */
function velocityshop_remove_query_strings($src) {
    $parts = explode('?ver', $src);
    return $parts[0];
}
add_filter('script_loader_src', 'velocityshop_remove_query_strings', 15, 1);
add_filter('style_loader_src', 'velocityshop_remove_query_strings', 15, 1);

/**
 * Add security headers
 */
function velocityshop_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }
}
add_action('send_headers', 'velocityshop_security_headers');

/**
 * Optimize database queries
 */
function velocityshop_optimize_queries() {
    // Remove unnecessary queries
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10);
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head', 10);
}
add_action('init', 'velocityshop_optimize_queries');

/**
 * Custom pagination
 */
function velocityshop_pagination() {
    global $wp_query;
    
    $big = 999999999;
    
    $paginate_links = paginate_links(array(
        'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
        'format' => '?paged=%#%',
        'current' => max(1, get_query_var('paged')),
        'total' => $wp_query->max_num_pages,
        'prev_text' => __('&laquo; Previous', 'velocityshop'),
        'next_text' => __('Next &raquo;', 'velocityshop'),
        'type' => 'array'
    ));
    
    if ($paginate_links) {
        echo '<nav class="pagination-wrapper" aria-label="' . __('Posts navigation', 'velocityshop') . '">';
        echo '<ul class="pagination">';
        foreach ($paginate_links as $link) {
            echo '<li>' . $link . '</li>';
        }
        echo '</ul>';
        echo '</nav>';
    }
}

/**
 * Get theme option with default fallback
 */
function velocityshop_get_option($option, $default = '') {
    return get_theme_mod('velocityshop_' . $option, $default);
}
