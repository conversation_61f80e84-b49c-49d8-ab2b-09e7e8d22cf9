<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @package VelocityShop
 * @version 1.0.0
 */

get_header(); ?>

<main class="site-main">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <section class="error-404 not-found">
                    <header class="page-header">
                        <h1 class="page-title"><?php esc_html_e('Oops! That page can&rsquo;t be found.', 'velocityshop'); ?></h1>
                    </header>

                    <div class="page-content">
                        <div class="error-404-content">
                            <div class="error-404-image">
                                <svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="100" cy="100" r="80" stroke="#e0e0e0" stroke-width="2" fill="none"/>
                                    <text x="100" y="110" text-anchor="middle" font-size="48" font-weight="bold" fill="#bdc3c7">404</text>
                                </svg>
                            </div>
                            
                            <div class="error-404-text">
                                <p><?php esc_html_e('It looks like nothing was found at this location. Maybe try one of the links below or a search?', 'velocityshop'); ?></p>
                                
                                <?php get_search_form(); ?>
                                
                                <div class="error-404-actions">
                                    <a href="<?php echo esc_url(home_url('/')); ?>" class="button button-primary">
                                        <?php esc_html_e('Go to Homepage', 'velocityshop'); ?>
                                    </a>
                                    
                                    <?php if (function_exists('wc_get_page_id')) : ?>
                                        <a href="<?php echo esc_url(get_permalink(wc_get_page_id('shop'))); ?>" class="button button-secondary">
                                            <?php esc_html_e('Visit Shop', 'velocityshop'); ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <?php if (function_exists('wc_get_page_id')) : ?>
                            <div class="error-404-products">
                                <h3><?php esc_html_e('Popular Products', 'velocityshop'); ?></h3>
                                <?php
                                $args = array(
                                    'post_type' => 'product',
                                    'posts_per_page' => 4,
                                    'meta_key' => 'total_sales',
                                    'orderby' => 'meta_value_num',
                                    'order' => 'DESC'
                                );
                                
                                $popular_products = new WP_Query($args);
                                
                                if ($popular_products->have_posts()) :
                                ?>
                                    <div class="products-grid">
                                        <?php while ($popular_products->have_posts()) : $popular_products->the_post(); ?>
                                            <div class="product-item">
                                                <a href="<?php the_permalink(); ?>">
                                                    <?php if (has_post_thumbnail()) : ?>
                                                        <?php the_post_thumbnail('medium', array('loading' => 'lazy')); ?>
                                                    <?php endif; ?>
                                                    <h4><?php the_title(); ?></h4>
                                                    <?php if (function_exists('wc_get_product')) : ?>
                                                        <?php $product = wc_get_product(get_the_ID()); ?>
                                                        <span class="price"><?php echo $product->get_price_html(); ?></span>
                                                    <?php endif; ?>
                                                </a>
                                            </div>
                                        <?php endwhile; ?>
                                    </div>
                                <?php
                                    wp_reset_postdata();
                                endif;
                                ?>
                            </div>
                        <?php endif; ?>

                        <div class="error-404-categories">
                            <h3><?php esc_html_e('Browse Categories', 'velocityshop'); ?></h3>
                            <?php
                            $categories = get_categories(array(
                                'orderby' => 'count',
                                'order'   => 'DESC',
                                'number'  => 6
                            ));
                            
                            if ($categories) :
                            ?>
                                <ul class="category-list">
                                    <?php foreach ($categories as $category) : ?>
                                        <li>
                                            <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>">
                                                <?php echo esc_html($category->name); ?>
                                                <span class="count">(<?php echo $category->count; ?>)</span>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php endif; ?>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</main>

<style>
.error-404 {
    text-align: center;
    padding: 4rem 0;
}

.error-404-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
}

.error-404-image svg {
    max-width: 100%;
    height: auto;
}

.error-404-text {
    max-width: 400px;
    text-align: left;
}

.error-404-text p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: #7f8c8d;
}

.error-404-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.button {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.button-primary {
    background: #3498db;
    color: white;
}

.button-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.button-secondary {
    background: transparent;
    color: #3498db;
    border: 2px solid #3498db;
}

.button-secondary:hover {
    background: #3498db;
    color: white;
}

.error-404-products,
.error-404-categories {
    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 1px solid #eee;
}

.error-404-products h3,
.error-404-categories h3 {
    margin-bottom: 2rem;
    color: #2c3e50;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.product-item {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.product-item:hover {
    transform: translateY(-5px);
}

.product-item a {
    display: block;
    text-decoration: none;
    color: inherit;
}

.product-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.product-item h4 {
    padding: 1rem;
    margin: 0;
    font-size: 1rem;
    color: #2c3e50;
}

.product-item .price {
    display: block;
    padding: 0 1rem 1rem;
    font-weight: 700;
    color: #27ae60;
}

.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.category-list li a {
    display: block;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 5px;
    text-decoration: none;
    color: #2c3e50;
    transition: all 0.3s ease;
}

.category-list li a:hover {
    background: #3498db;
    color: white;
}

.category-list .count {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.category-list li a:hover .count {
    color: rgba(255,255,255,0.8);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .error-404-content {
        flex-direction: column;
        gap: 2rem;
    }
    
    .error-404-text {
        text-align: center;
    }
    
    .error-404-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .category-list {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>
