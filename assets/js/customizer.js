/**
 * VelocityShop Theme Customizer JS
 * 
 * @package VelocityShop
 * @version 1.0.0
 */

(function($) {
    'use strict';

    // Site title and description
    wp.customize('blogname', function(value) {
        value.bind(function(to) {
            $('.site-title a').text(to);
        });
    });

    wp.customize('blogdescription', function(value) {
        value.bind(function(to) {
            $('.site-description').text(to);
        });
    });

    // Header text color
    wp.customize('header_textcolor', function(value) {
        value.bind(function(to) {
            if ('blank' === to) {
                $('.site-title, .site-description').css({
                    'clip': 'rect(1px, 1px, 1px, 1px)',
                    'position': 'absolute'
                });
            } else {
                $('.site-title, .site-description').css({
                    'clip': 'auto',
                    'position': 'relative'
                });
                $('.site-title a, .site-description').css({
                    'color': to
                });
            }
        });
    });

    // Primary color
    wp.customize('velocityshop_primary_color', function(value) {
        value.bind(function(to) {
            updateColorVariable('--primary-color', to);
            
            // Update specific elements
            $('<style id="primary-color-style">')
                .html(`
                    a, .nav-menu a:hover, .woocommerce .button {
                        color: ${to} !important;
                    }
                    .woocommerce .button, .woocommerce button.button, .woocommerce input.button {
                        background: ${to} !important;
                    }
                    .nav-menu a::after {
                        background: ${to} !important;
                    }
                `)
                .appendTo('head');
        });
    });

    // Secondary color
    wp.customize('velocityshop_secondary_color', function(value) {
        value.bind(function(to) {
            updateColorVariable('--secondary-color', to);
            
            $('<style id="secondary-color-style">')
                .html(`
                    h1, h2, h3, h4, h5, h6, .site-logo {
                        color: ${to} !important;
                    }
                `)
                .appendTo('head');
        });
    });

    // Accent color
    wp.customize('velocityshop_accent_color', function(value) {
        value.bind(function(to) {
            updateColorVariable('--accent-color', to);
            
            $('<style id="accent-color-style">')
                .html(`
                    .woocommerce .button.alt {
                        background: ${to} !important;
                    }
                    .woocommerce ul.products li.product .price {
                        color: ${to} !important;
                    }
                `)
                .appendTo('head');
        });
    });

    // Container width
    wp.customize('velocityshop_container_width', function(value) {
        value.bind(function(to) {
            updateColorVariable('--container-width', to + 'px');
            
            $('<style id="container-width-style">')
                .html(`
                    .container {
                        max-width: ${to}px !important;
                    }
                `)
                .appendTo('head');
        });
    });

    // Body font
    wp.customize('velocityshop_body_font', function(value) {
        value.bind(function(to) {
            const fontFamily = getFontFamily(to);
            
            // Load Google Font if needed
            if (to !== 'system') {
                loadGoogleFont(to);
            }
            
            $('<style id="body-font-style">')
                .html(`
                    body {
                        font-family: ${fontFamily} !important;
                    }
                `)
                .appendTo('head');
        });
    });

    // Heading font
    wp.customize('velocityshop_heading_font', function(value) {
        value.bind(function(to) {
            const fontFamily = getFontFamily(to);
            
            // Load Google Font if needed
            if (to !== 'system') {
                loadGoogleFont(to);
            }
            
            $('<style id="heading-font-style">')
                .html(`
                    h1, h2, h3, h4, h5, h6 {
                        font-family: ${fontFamily} !important;
                    }
                `)
                .appendTo('head');
        });
    });

    // Products per row
    wp.customize('velocityshop_products_per_row', function(value) {
        value.bind(function(to) {
            const minWidth = getProductMinWidth(to);
            
            $('<style id="products-per-row-style">')
                .html(`
                    .woocommerce ul.products {
                        grid-template-columns: repeat(auto-fit, minmax(${minWidth}px, 1fr)) !important;
                    }
                `)
                .appendTo('head');
        });
    });

    // Helper functions
    function updateColorVariable(variable, value) {
        const root = document.documentElement;
        root.style.setProperty(variable, value);
    }

    function getFontFamily(font) {
        const families = {
            'system': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif',
            'roboto': '"Roboto", sans-serif',
            'open-sans': '"Open Sans", sans-serif',
            'lato': '"Lato", sans-serif',
            'montserrat': '"Montserrat", sans-serif',
            'playfair': '"Playfair Display", serif'
        };
        
        return families[font] || families['system'];
    }

    function loadGoogleFont(font) {
        const fonts = {
            'roboto': 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap',
            'open-sans': 'https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap',
            'lato': 'https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap',
            'montserrat': 'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap',
            'playfair': 'https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap'
        };
        
        if (fonts[font] && !$(`link[href="${fonts[font]}"]`).length) {
            $('<link>')
                .attr('rel', 'stylesheet')
                .attr('href', fonts[font])
                .appendTo('head');
        }
    }

    function getProductMinWidth(columns) {
        const widths = {
            '2': '400',
            '3': '280',
            '4': '220'
        };
        
        return widths[columns] || '280';
    }

    // Live preview enhancements
    function initLivePreview() {
        // Add visual feedback for customizer changes
        $('body').addClass('customizer-preview');
        
        // Highlight elements on hover in customizer
        $(document).on('mouseenter', '[data-customize-partial-id]', function() {
            $(this).addClass('customizer-highlight');
        }).on('mouseleave', '[data-customize-partial-id]', function() {
            $(this).removeClass('customizer-highlight');
        });
    }

    // Initialize when customizer is ready
    wp.customize.bind('ready', function() {
        initLivePreview();
        
        // Add custom CSS for customizer preview
        $('<style>')
            .html(`
                .customizer-preview .customizer-highlight {
                    outline: 2px dashed #0073aa !important;
                    outline-offset: 2px !important;
                }
                
                .customizer-preview #primary-color-style,
                .customizer-preview #secondary-color-style,
                .customizer-preview #accent-color-style,
                .customizer-preview #container-width-style,
                .customizer-preview #body-font-style,
                .customizer-preview #heading-font-style,
                .customizer-preview #products-per-row-style {
                    /* Ensure customizer styles take precedence */
                }
            `)
            .appendTo('head');
    });

    // Smooth transitions for customizer changes
    function addCustomizerTransitions() {
        $('<style>')
            .html(`
                .customizer-preview * {
                    transition: color 0.3s ease, background-color 0.3s ease, 
                               border-color 0.3s ease, font-family 0.3s ease !important;
                }
                
                .customizer-preview .container {
                    transition: max-width 0.3s ease !important;
                }
                
                .customizer-preview .woocommerce ul.products {
                    transition: grid-template-columns 0.3s ease !important;
                }
            `)
            .appendTo('head');
    }

    // Initialize transitions
    $(document).ready(function() {
        if (wp.customize) {
            addCustomizerTransitions();
        }
    });

    // Handle selective refresh
    wp.customize.selectiveRefresh.bind('partial-content-rendered', function(placement) {
        // Re-initialize any JavaScript for refreshed content
        if (placement.partial.id === 'blogname' || placement.partial.id === 'blogdescription') {
            // Reinitialize any logo/title related functionality
            console.log('Partial refresh completed for:', placement.partial.id);
        }
    });

})(jQuery);
