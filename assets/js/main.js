/**
 * VelocityShop Theme JavaScript
 * 
 * @package VelocityShop
 * @version 1.0.0
 */

(function($) {
    'use strict';

    // DOM Ready
    $(document).ready(function() {
        initMobileMenu();
        initSearchToggle();
        initBackToTop();
        initLazyLoading();
        initSmoothScrolling();
        initCartUpdates();
        initProductGallery();
        initPerformanceOptimizations();
    });

    /**
     * Mobile Menu Toggle
     */
    function initMobileMenu() {
        $('.mobile-menu-toggle').on('click', function(e) {
            e.preventDefault();
            
            const $nav = $('.main-navigation');
            const $button = $(this);
            const isExpanded = $button.attr('aria-expanded') === 'true';
            
            $nav.toggleClass('active');
            $button.attr('aria-expanded', !isExpanded);
            
            // Animate the hamburger icon
            $button.toggleClass('active');
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.site-header').length) {
                $('.main-navigation').removeClass('active');
                $('.mobile-menu-toggle').attr('aria-expanded', 'false').removeClass('active');
            }
        });

        // Close mobile menu on window resize
        $(window).on('resize', function() {
            if ($(window).width() > 768) {
                $('.main-navigation').removeClass('active');
                $('.mobile-menu-toggle').attr('aria-expanded', 'false').removeClass('active');
            }
        });
    }

    /**
     * Search Toggle
     */
    function initSearchToggle() {
        $('.search-toggle').on('click', function(e) {
            e.preventDefault();
            
            const $searchForm = $('.header-search');
            const $button = $(this);
            const isExpanded = $button.attr('aria-expanded') === 'true';
            
            $searchForm.slideToggle(300, function() {
                if ($searchForm.is(':visible')) {
                    $searchForm.find('.search-field').focus();
                }
            });
            
            $button.attr('aria-expanded', !isExpanded);
        });

        // Close search when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.header-search, .search-toggle').length) {
                $('.header-search').slideUp(300);
                $('.search-toggle').attr('aria-expanded', 'false');
            }
        });
    }

    /**
     * Back to Top Button
     */
    function initBackToTop() {
        const $backToTop = $('#back-to-top');
        
        // Show/hide button based on scroll position
        $(window).on('scroll', function() {
            if ($(this).scrollTop() > 300) {
                $backToTop.fadeIn();
            } else {
                $backToTop.fadeOut();
            }
        });

        // Smooth scroll to top
        $backToTop.on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: 0
            }, 600);
        });
    }

    /**
     * Lazy Loading for Images
     */
    function initLazyLoading() {
        // Use Intersection Observer for better performance
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                        }
                        observer.unobserve(img);
                    }
                });
            });

            // Observe all images with data-src attribute
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Smooth Scrolling for Anchor Links
     */
    function initSmoothScrolling() {
        $('a[href*="#"]:not([href="#"])').on('click', function(e) {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && 
                location.hostname === this.hostname) {
                
                const target = $(this.hash);
                const $target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                
                if ($target.length) {
                    e.preventDefault();
                    $('html, body').animate({
                        scrollTop: $target.offset().top - 100
                    }, 600);
                }
            }
        });
    }

    /**
     * Cart Updates (AJAX)
     */
    function initCartUpdates() {
        // Update cart count in header
        $(document.body).on('added_to_cart removed_from_cart updated_wc_div', function() {
            updateCartCount();
        });

        function updateCartCount() {
            $.ajax({
                url: velocityshop_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_cart_count',
                    nonce: velocityshop_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        const $cartCount = $('.cart-count');
                        if (response.data.count > 0) {
                            $cartCount.text(response.data.count).show();
                        } else {
                            $cartCount.hide();
                        }
                    }
                }
            });
        }

        // Add to cart button loading state
        $(document).on('click', '.single_add_to_cart_button', function() {
            $(this).addClass('loading').text('Adding...');
        });

        // Remove loading state after cart update
        $(document.body).on('added_to_cart', function() {
            $('.single_add_to_cart_button').removeClass('loading').text('Add to cart');
        });
    }

    /**
     * Product Gallery Enhancements
     */
    function initProductGallery() {
        // Product image zoom on hover
        $('.woocommerce-product-gallery__image').on('mouseenter', function() {
            $(this).find('img').css('transform', 'scale(1.1)');
        }).on('mouseleave', function() {
            $(this).find('img').css('transform', 'scale(1)');
        });

        // Product thumbnail navigation
        $('.flex-control-thumbs img').on('click', function() {
            $('.flex-control-thumbs img').removeClass('flex-active');
            $(this).addClass('flex-active');
        });
    }

    /**
     * Performance Optimizations
     */
    function initPerformanceOptimizations() {
        // Debounce scroll events
        let scrollTimer;
        $(window).on('scroll', function() {
            clearTimeout(scrollTimer);
            scrollTimer = setTimeout(function() {
                // Trigger custom scroll event
                $(window).trigger('scroll.debounced');
            }, 10);
        });

        // Preload critical pages
        const criticalPages = [
            $('.nav-menu a[href*="shop"]').attr('href'),
            $('.nav-menu a[href*="cart"]').attr('href'),
            $('.nav-menu a[href*="checkout"]').attr('href')
        ];

        criticalPages.forEach(url => {
            if (url) {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = url;
                document.head.appendChild(link);
            }
        });

        // Optimize images on scroll
        $(window).on('scroll.debounced', function() {
            $('img[loading="lazy"]').each(function() {
                const rect = this.getBoundingClientRect();
                if (rect.top < window.innerHeight + 200) {
                    this.loading = 'eager';
                }
            });
        });
    }

    /**
     * WooCommerce Specific Enhancements
     */
    if (typeof wc_add_to_cart_params !== 'undefined') {
        // Enhanced add to cart functionality
        $(document).on('click', '.add_to_cart_button:not(.product_type_variable)', function(e) {
            const $button = $(this);
            
            // Add loading animation
            $button.addClass('loading');
            
            // Create a mini notification
            setTimeout(function() {
                if (!$button.hasClass('added')) {
                    showNotification('Adding to cart...', 'info');
                }
            }, 500);
        });

        // Show success notification
        $(document.body).on('added_to_cart', function(event, fragments, cart_hash, $button) {
            showNotification('Product added to cart!', 'success');
            
            // Animate cart icon
            $('.cart-toggle').addClass('bounce');
            setTimeout(function() {
                $('.cart-toggle').removeClass('bounce');
            }, 600);
        });
    }

    /**
     * Notification System
     */
    function showNotification(message, type = 'info') {
        const $notification = $(`
            <div class="velocityshop-notification ${type}">
                <span>${message}</span>
                <button class="close">&times;</button>
            </div>
        `);

        $('body').append($notification);
        
        setTimeout(function() {
            $notification.addClass('show');
        }, 100);

        // Auto hide after 3 seconds
        setTimeout(function() {
            hideNotification($notification);
        }, 3000);

        // Manual close
        $notification.find('.close').on('click', function() {
            hideNotification($notification);
        });
    }

    function hideNotification($notification) {
        $notification.removeClass('show');
        setTimeout(function() {
            $notification.remove();
        }, 300);
    }

    /**
     * Accessibility Enhancements
     */
    function initAccessibility() {
        // Skip link functionality
        $('.skip-link').on('click', function(e) {
            const target = $(this.getAttribute('href'));
            if (target.length) {
                target.focus();
                if (target.is(':focus')) {
                    return false;
                } else {
                    target.attr('tabindex', '-1');
                    target.focus();
                }
            }
        });

        // Keyboard navigation for dropdowns
        $('.nav-menu a').on('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                $(this).click();
            }
        });
    }

    // Initialize accessibility features
    initAccessibility();

})(jQuery);

// Add CSS for notifications and animations
const notificationCSS = `
<style>
.velocityshop-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #fff;
    border-left: 4px solid #3498db;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 1rem 1.5rem;
    border-radius: 4px;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.velocityshop-notification.show {
    transform: translateX(0);
}

.velocityshop-notification.success {
    border-color: #27ae60;
}

.velocityshop-notification.error {
    border-color: #e74c3c;
}

.velocityshop-notification .close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    float: right;
    margin-left: 1rem;
    color: #7f8c8d;
}

.cart-toggle.bounce {
    animation: bounce 0.6s ease;
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    80% { transform: translateY(-5px); }
}

.mobile-menu-toggle.active {
    transform: rotate(90deg);
}

.loading {
    opacity: 0.7;
    pointer-events: none;
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', notificationCSS);
