/*
Theme Name: VelocityShop
Description: A premium, high-performance WordPress theme specifically optimized for WooCommerce online stores. Features lightning-fast loading times, mobile-responsive design, and seamless e-commerce integration.
Author: VelocityShop Team
Version: 1.0.0
Requires at least: 6.0
Tested up to: 6.7
Requires PHP: 8.0
License: Commercial License
License URI: LICENSE.txt
Text Domain: velocityshop
Tags: e-commerce, woocommerce, responsive, fast-loading, modern, clean, shop, store, business
WC requires at least: 8.0
WC tested up to: 9.4
*/

/* ==========================================================================
   Reset and Base Styles
   ========================================================================== */

*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-weight: 400;
    color: #333;
    background-color: #fff;
    overflow-x: hidden;
}

/* ==========================================================================
   Typography
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: #2c3e50;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover,
a:focus {
    color: #2980b9;
    outline: none;
}

/* ==========================================================================
   Layout Components
   ========================================================================== */

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* ==========================================================================
   Header Styles
   ========================================================================== */

.site-header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.site-logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

.site-logo img {
    max-height: 50px;
    width: auto;
}

/* Navigation */
.main-navigation {
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin: 0 1rem;
}

.nav-menu a {
    color: #333;
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a:focus {
    color: #3498db;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #3498db;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-toggle,
.cart-toggle,
.account-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #333;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.search-toggle:hover,
.cart-toggle:hover,
.account-toggle:hover {
    background: #f8f9fa;
    color: #3498db;
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #333;
    cursor: pointer;
    padding: 0.5rem;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .col-sm-12 { flex: 0 0 100%; }
    .col-sm-6 { flex: 0 0 50%; }
    .col-sm-4 { flex: 0 0 33.333333%; }
    
    .header-container {
        padding: 0.75rem 0;
    }
    
    .main-navigation {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #fff;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        padding: 1rem 0;
    }
    
    .main-navigation.active {
        display: block;
    }
    
    .nav-menu {
        flex-direction: column;
        padding: 0 20px;
    }
    
    .nav-menu li {
        margin: 0.5rem 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
}

/* ==========================================================================
   WooCommerce Styles
   ========================================================================== */

/* Shop Page */
.woocommerce ul.products {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.woocommerce ul.products li.product {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.woocommerce ul.products li.product:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.woocommerce ul.products li.product .woocommerce-loop-product__link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.woocommerce ul.products li.product img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.woocommerce ul.products li.product:hover img {
    transform: scale(1.05);
}

.woocommerce ul.products li.product .woocommerce-loop-product__title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 1rem;
    color: #2c3e50;
    line-height: 1.4;
}

.woocommerce ul.products li.product .price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #27ae60;
    margin: 0 1rem 1rem;
}

.woocommerce ul.products li.product .price del {
    color: #95a5a6;
    font-weight: 400;
    margin-right: 0.5rem;
}

/* Add to Cart Button */
.woocommerce ul.products li.product .button {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: pointer;
    margin: 0 1rem 1rem;
    display: inline-block;
    text-align: center;
    width: calc(100% - 2rem);
}

.woocommerce ul.products li.product .button:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

/* Product Single Page */
.woocommerce div.product {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin: 2rem 0;
}

.woocommerce div.product .woocommerce-product-gallery {
    position: relative;
}

.woocommerce div.product .woocommerce-product-gallery img {
    width: 100%;
    border-radius: 8px;
}

.woocommerce div.product .summary {
    padding: 1rem 0;
}

.woocommerce div.product .product_title {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.woocommerce div.product .price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #27ae60;
    margin-bottom: 1.5rem;
}

.woocommerce div.product .woocommerce-product-details__short-description {
    margin-bottom: 2rem;
    line-height: 1.7;
    color: #555;
}

/* Cart and Checkout */
.woocommerce table.cart {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
}

.woocommerce table.cart th,
.woocommerce table.cart td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    text-align: left;
}

.woocommerce table.cart th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.woocommerce .cart-collaterals {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
    margin-top: 2rem;
}

.woocommerce .cart_totals h2 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

/* Buttons */
.woocommerce .button,
.woocommerce button.button,
.woocommerce input.button {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
    text-decoration: none;
}

.woocommerce .button:hover,
.woocommerce button.button:hover,
.woocommerce input.button:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.woocommerce .button.alt {
    background: #27ae60;
}

.woocommerce .button.alt:hover {
    background: #229954;
}

/* Messages */
.woocommerce .woocommerce-message,
.woocommerce .woocommerce-info,
.woocommerce .woocommerce-error {
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.woocommerce .woocommerce-message {
    background: #d4edda;
    border-color: #27ae60;
    color: #155724;
}

.woocommerce .woocommerce-info {
    background: #d1ecf1;
    border-color: #3498db;
    color: #0c5460;
}

.woocommerce .woocommerce-error {
    background: #f8d7da;
    border-color: #e74c3c;
    color: #721c24;
}
